-- main.lua - Air780EG Enhanced Firmware
PROJECT = "mqtt_sensor_demo"
VERSION = "1.0.0"
PRODUCT_KEY = "AtqpMY7HfMCjxCgLw200LlNN2ZsiVszc"

-- Try to set version info for libfota before it loads
_G.FOTA_VERSION = VERSION
_G.APP_VERSION = VERSION

-- Load core modules
local sys = require("sys")
local PinModule = require("pin_module")
local vars = require("variables")
local my_utils = require("my_utils")

-- Load optional modules
local update_available, update = pcall(require, "update")
if not update_available then
    local ota_available, ota_update = pcall(require, "ota_update")
    if ota_available then
        update = ota_update
        update_available = true
    else
        update = {request = function() return false end}
        update_available = false
    end
end

local mqtt_module_available, mqtt_module = pcall(require, "mqtt_module")
if not mqtt_module_available then
    mqtt_module = {
        init = function() return false end,
        publish = function() return false end,
        is_ready = function() return false end,
        get_client_id = function() return "unknown" end
    }
end

-- Configuration
local PRINT_INTERVAL = 5000
local mqtt_connection_beep_played = false
local ota_in_progress = false
local last_voltage = 0

-- Time tracking
sys.timerLoopStart(function() vars.currentTime = vars.currentTime + 1 end, 1000)

-- Sensor configs
local SHTC3 = {i2c_id = 0, addr = 0x70, cmd_wakeup = 0x3517, cmd_sleep = 0xB098,
               cmd_measure_normal = 0x7866, temp_offset = 0}
local GPS = require("libgnss_gps")
local ADC_CONFIG = {id = 1, channel = 0, scaling_factor = 16.14}

-- Simple logging
if not log then
    _G.log = {
        info = function(tag, ...) print("[INFO]", ...) end,
        warn = function(tag, ...) print("[WARN]", ...) end,
        error = function(tag, ...) print("[ERROR]", ...) end
    }
end

-- Simple JSON encoder
if not json then
    _G.json = {
        encode = function(obj)
            if type(obj) ~= "table" then return tostring(obj) end
            local res = "{"
            for k, v in pairs(obj) do
                res = res .. '"' .. k .. '":' .. (type(v) == "string" and '"' .. v .. '"' or tostring(v)) .. ","
            end
            return res:sub(1, -2) .. "}"
        end
    }
end

-- Initialize SHTC3 sensor
local function initSHTC3()
    local setup_result = i2c.setup(SHTC3.i2c_id, 100000)
    if setup_result ~= 1 then return false end

    local result = i2c.send(SHTC3.i2c_id, SHTC3.addr, {0x00})
    if not result then return false end

    print("SHTC3 sensor initialized")
    return true
end

-- Sensor functions
local function readSHTC3()
    local wakeup_cmd_high = (SHTC3.cmd_wakeup >> 8) & 0xFF
    local wakeup_cmd_low = SHTC3.cmd_wakeup & 0xFF
    i2c.send(SHTC3.i2c_id, SHTC3.addr, {wakeup_cmd_high, wakeup_cmd_low})
    sys.wait(1)

    local measure_cmd_high = (SHTC3.cmd_measure_normal >> 8) & 0xFF
    local measure_cmd_low = SHTC3.cmd_measure_normal & 0xFF
    i2c.send(SHTC3.i2c_id, SHTC3.addr, {measure_cmd_high, measure_cmd_low})
    sys.wait(15)

    local data = i2c.recv(SHTC3.i2c_id, SHTC3.addr, 6)
    if not data or #data ~= 6 then return {temperature = 25.0, humidity = 50.0} end

    local temp_raw = (data:byte(1) << 8) | data:byte(2)
    local humidity_raw = (data:byte(4) << 8) | data:byte(5)
    local temperature = -45.0 + 175.0 * (temp_raw / 65535.0) + SHTC3.temp_offset
    local humidity = 100.0 * (humidity_raw / 65535.0)

    local sleep_cmd_high = (SHTC3.cmd_sleep >> 8) & 0xFF
    local sleep_cmd_low = SHTC3.cmd_sleep & 0xFF
    i2c.send(SHTC3.i2c_id, SHTC3.addr, {sleep_cmd_high, sleep_cmd_low})

    return {temperature = temperature, humidity = humidity}
end

local function initGPS()
    if not GPS then return false end
    local success = GPS.init()
    if success then print("GPS initialized") end
    return success
end

local function getGPSPosition()
    return GPS and GPS.getPosition() or nil
end

local function initADC()
    local result = adc.open(ADC_CONFIG.id, ADC_CONFIG.channel)
    if result == true or result == 1 then
        print("ADC initialized")
        return true
    end
    return false
end

local function readVoltage()
    local adcval, voltage_mv = adc.read(ADC_CONFIG.id, ADC_CONFIG.channel)
    if not voltage_mv then return 12.0 end
    return (voltage_mv * ADC_CONFIG.scaling_factor) / 1000.0 + (vars.voltage_offset or 0)
end

local function getRSSI()
    if mobile and mobile.signal then
        return mobile.signal() or 0
    elseif mobile and mobile.csq then
        return mobile.csq() or 0
    end
    return 0
end

-- Format sensor data as JSON
local function formatSensorData(temp, humidity, voltage, gps, rssi)
    local lat_str, lon_str = "0 N", "0 E"
    if gps and gps.latitude and gps.longitude then
        local lat_deg = math.floor(gps.latitude / 100)
        local lat_min = gps.latitude - (lat_deg * 100)
        lat_str = string.format("%.5f N", lat_deg + (lat_min / 60))

        local lon_deg = math.floor(gps.longitude / 100)
        local lon_min = gps.longitude - (lon_deg * 100)
        lon_str = string.format("%.5f E", lon_deg + (lon_min / 60))
    end

    return string.format('{"Lon":"%s","Lat":"%s","hum":%d,"ver":"%s","rssi":%d,"volt":%.3f,"Speed":%d,"temp":%d,"motion":0,"light":0}',
        lon_str, lat_str, math.floor((humidity or 29) + 0.5), VERSION, rssi or 0,
        voltage or 10.0, gps and gps.speed or 0, math.floor((temp or 30) + 0.5))
end

-- Consolidated command functions
local function playBeep(pattern)
    if vars.sound_flag then
        sys.taskInit(function()
            if pattern == "check" then PinModule.beepPattern(1, 300, 0, 2000)
            elseif pattern == "lock" then PinModule.beepPattern(2, 200, 200, 2500)
            elseif pattern == "unlock" then PinModule.beepPattern(3, 150, 150, 3000)
            elseif pattern == "as" then PinModule.beepPattern(1, 500, 0, 1800)
            else PinModule.beepPattern(1, 200, 0, 2000) end
        end)
    end
end

local function lockCommand()
    playBeep("lock")
    sys.taskInit(function()
        sys.wait(vars.lock_wait_duration or 2000)
        PinModule.relayControl("Key1", 1)
        sys.wait(vars.lock_press_duration or 1000)
        PinModule.relayControl("Key1", 0)
        if not vars.key_state then PinModule.relayControl("KeyPower", 0) end
    end)
end

local function unlockCommand()
    playBeep("unlock")
    sys.taskInit(function()
        sys.wait(vars.unlock_wait_duration or 1000)
        PinModule.relayControl("Key2", 1)
        sys.wait(vars.unlock_press_duration or 1000)
        PinModule.relayControl("Key2", 0)
        if not vars.key_state then PinModule.relayControl("KeyPower", 0) end
    end)
end

local function asCommand()
    if not vars.isLicensed then return end
    playBeep("as")
    PinModule.relayControl("KeyPower", 1)
    vars.carAlreadyStarted = true
    sys.taskInit(function()
        sys.wait(vars.lock_init_duration or 2000)
        PinModule.relayControl("Relay1", 1)
        sys.wait(vars.relay1_on_duration or 3000)
        PinModule.relayControl("Relay1", 0)
    end)
end

local function untarCommand()
    sys.taskInit(function()
        playBeep("untar")
        sys.wait(2000)
        PinModule.relayControl("Relay1", 0)
        PinModule.relayControl("Relay2", 0)
        PinModule.relayControl("Relay3", 0)
        if not vars.key_state then PinModule.relayControl("KeyPower", 0) end
        vars.carAlreadyStarted = false
    end)
end

-- Simplified SMS functions
local function sendSms(phoneNumber, message)
    if sms then
        -- Remove + prefix if present, as some SMS libraries handle international format differently
        if string.match(phoneNumber, "^%+") then
            phoneNumber = string.sub(phoneNumber, 2)  -- Remove the + prefix
        end
        return sms.send(phoneNumber, message)
    end
    return false
end

local function smsCallback(num, data, datetime)
    local original_num = num  -- Store the original full number for sending replies
    local short_num = string.sub(num, -8)  -- Extract last 8 digits for comparison

    -- Forward special keywords to MQTT
    local keywords = {"Tand", "TG", "hugatsaa", "kod", "code", "opt"}
    for _, keyword in ipairs(keywords) do
        if string.find(data, keyword) then
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"sms":"' .. data .. '"}')
            end
            return
        end
    end

    -- Handle restart command
    if string.lower(data) == "restart" then
        sendSms(original_num, "System restarting...")
        sys.taskInit(function()
            sys.wait(2000)
            if rtos and rtos.reboot then rtos.reboot() end
        end)
        return
    end

    -- Check authorization (compare using short numbers)
    if short_num ~= vars.phone_number1 and short_num ~= vars.phone_number2 and short_num ~= vars.phone_number3 then
        if string.len(short_num) >= 8 then
            sendSms(original_num, "taniulaagvi dugaar!")  -- Send to original full number
        end
        return
    end

    -- Store for processing (use original number for replies)
    vars.sms_data = data
    vars.callback_number = original_num
end

-- Initialize SMS
local function initSMS()
    if sms then
        sms.setNewSmsCb(smsCallback)
        print("SMS initialized")
        return true
    end
    return false
end

-- Task to read and print sensor data
local function sensorTask()
    print("Starting sensor reading task")

    -- Wait for system to stabilize
    sys.wait(2000)

    while true do
        -- Read sensor data
        local temp_hum = readSHTC3()
        local temperature = temp_hum and temp_hum.temperature or 25.0
        local humidity = temp_hum and temp_hum.humidity or 50.0
        local voltage = readVoltage() or 12.0
        local gps = getGPSPosition()
        local rssi = getRSSI() or 0

        -- Format data as JSON
        local json_data = formatSensorData(temperature, humidity, voltage, gps, rssi)

        -- Print sensor data in compact format
        print(json_data)
        print("T:" .. string.format("%.1f", temperature) .. "°C H:" .. string.format("%.1f", humidity) .. "% V:" .. string.format("%.2f", voltage) .. "V RSSI:" .. rssi .. " GPS:" .. (gps and "OK" or "NO"))



        -- Wait for the next reading interval
        sys.wait(PRINT_INTERVAL)
    end
end

-- Initialize mobile network
local function initMobile()
    print("\n=== Mobile Network Initialization Start ===")

    -- Check if mobile module is available
    if not mobile then
        print("Mobile module not available")
        return false
    end

    -- Wait for network registration
    print("Waiting for network registration...")
    local timeout = 30  -- 30 seconds timeout
    local start_time = os.time()

    while os.time() - start_time < timeout do
        -- Check network registration status
        if mobile.status then
            local status = mobile.status()
            print("Network status: " .. tostring(status))

            -- Different versions of LuatOS may return different status formats
            -- Some return strings like "REGISTERED", others return numbers like 1 or 2
            if status == "REGISTERED" or status == "REGISTERED_ROAMING" or
               status == 1 or status == 2 or status == 5 then
                print("Network registered successfully")

                -- Get signal strength
                local rssi = getRSSI()
                print("Signal strength (RSSI): " .. rssi .. " dBm")

                -- Get operator info if available
                if mobile.getOperator then
                    local operator = mobile.getOperator()
                    print("Network operator: " .. (operator or "Unknown"))
                end

                print("Mobile network initialized successfully")
                -- Turn on NetLED when network is registered
                PinModule.setNetLED(1)
                print("=== Mobile Network Initialization Complete ===\n")
                return true
            end
        end

        sys.wait(1000)  -- Check every second
    end

    -- Even if registration times out, we can still try to get signal strength
    local rssi = getRSSI()
    if rssi and rssi ~= 0 then
        print("Network registration timed out, but signal detected (RSSI: " .. rssi .. " dBm)")
        print("Mobile network partially initialized")
        -- Turn on NetLED when signal is detected even without full registration
        PinModule.setNetLED(1)
        print("=== Mobile Network Initialization Complete with Warnings ===\n")
        return true
    end

    print("Warning: Network registration timeout")
    print("=== Mobile Network Initialization Complete with Warnings ===\n")
    return false
end

-- Function to publish sensor data to MQTT
-- This function starts a task to publish data and returns immediately
local function publishSensorData()
    -- Start a task to handle the publishing (allows yielding)
    sys.taskInit(function()
        -- Check if MQTT is connected
        if mqtt_module.is_ready() then
            -- Read sensor data
            local temp_hum = readSHTC3()
            local temperature = temp_hum and temp_hum.temperature or 25.0
            local humidity = temp_hum and temp_hum.humidity or 50.0
            local voltage = readVoltage() or 12.0
            local gps = getGPSPosition()
            local rssi = getRSSI() or 0

            -- Format and publish sensor data
            local json_data = formatSensorData(temperature, humidity, voltage, gps, rssi)
            local publish_result = mqtt_module.publish(json_data)

            if publish_result then
                print("Published sensor data to MQTT")
                -- Publish a success event that other parts of the code can listen for
                sys.publish("MQTT_PUBLISH_SUCCESS")
            else
                print("Failed to publish sensor data to MQTT")
                -- Publish a failure event that other parts of the code can listen for
                sys.publish("MQTT_PUBLISH_FAILURE")
            end
        else
            print("MQTT not connected, skipping publish")
            -- Publish a failure event that other parts of the code can listen for
            sys.publish("MQTT_PUBLISH_FAILURE")
        end
    end)

    -- Return true to indicate that the task was started successfully
    return true
end

-- Enhanced UART task using official LuatOS UART API
local function uartTask()
    local lastTime = {lock = 0, unlock = 0, mirror = 0}
    local uart_initialized = false
    local uart_id = 1  -- UART1 for Air780EG
    local baud_rate = 9600

    print("=== UART INITIALIZATION START (LuatOS API) ===")
    print("UART module available: " .. tostring(uart ~= nil))

    if not uart then
        print("ERROR: UART module not available!")
        print("=== UART INITIALIZATION FAILED ===")
        return
    end

    print("Available UART functions:")
    print("  uart.setup: " .. tostring(uart.setup ~= nil))
    print("  uart.read: " .. tostring(uart.read ~= nil))
    print("  uart.write: " .. tostring(uart.write ~= nil))
    print("  uart.rxSize: " .. tostring(uart.rxSize ~= nil))
    print("  uart.on: " .. tostring(uart.on ~= nil))
    print("  uart.exist: " .. tostring(uart.exist ~= nil))

    -- Check if UART1 exists
    if uart.exist then
        local uart1_exists = uart.exist(1)
        local uart0_exists = uart.exist(0)
        print("UART0 exists: " .. tostring(uart0_exists))
        print("UART1 exists: " .. tostring(uart1_exists))

        if not uart1_exists and not uart0_exists then
            print("❌ No UART ports available!")
            return
        end

        -- Use UART1 if available, otherwise UART0
        uart_id = uart1_exists and 1 or 0
    end

    -- Configure GPIO18 (Pin 17) for MAIN_RXD
    if gpio then
        print("Configuring Pin 17 (GPIO18/MAIN_RXD):")
        local gpio18_success = pcall(function()
            gpio.setup(18, nil, gpio.PULLUP)  -- Pin 17 = GPIO18 = MAIN_RXD with pullup
        end)
        print("Pin 17 (GPIO18/MAIN_RXD) setup: " .. tostring(gpio18_success))
    end

    -- Initialize UART using official LuatOS API
    print("Initializing UART" .. uart_id .. " with LuatOS API...")
    local setup_result = uart.setup(
        uart_id,           -- UART ID
        baud_rate,         -- Baud rate: 9600
        8,                 -- Data bits: 8
        1,                 -- Stop bits: 1
        uart.NONE,         -- Parity: None
        uart.LSB,          -- Bit order: LSB (default)
        1024               -- Buffer size: 1024 bytes
    )

    if setup_result == 0 then
        uart_initialized = true
        print("✅ UART" .. uart_id .. " initialized successfully")
        print("Configuration: " .. baud_rate .. " baud, 8N1, 1024 byte buffer")
        print("Pin 17 (GPIO18/MAIN_RXD) ready for receive-only operation")
    else
        print("❌ UART" .. uart_id .. " initialization failed, result: " .. tostring(setup_result))
        return
    end

    -- Set up UART event handler using official LuatOS API
    if uart_initialized and uart.on then
        print("Setting up UART event handler...")
        uart.on(uart_id, "receive", function(id, len)
            print("=== UART RECEIVE EVENT ===")
            print("UART ID: " .. id .. ", Data length: " .. len .. " bytes")

            -- Read the data
            local data = uart.read(id, len)
            if data and #data > 0 then
                print("Raw data length: " .. #data)
                print("Raw data (hex): " .. string.gsub(data, ".", function(c)
                    return string.format("%02X ", string.byte(c))
                end))
                print("Raw data (string): [" .. data .. "]")

                -- Process command if data is long enough
                if #data > 8 then
                    local cmd_id = data:sub(9, 9)
                    print("Command ID at position 9: [" .. cmd_id .. "]")

                    local now = vars.currentTime
                    print("Current time: " .. now)

                    -- Execute commands with timing protection
                    if cmd_id == "8" and now - lastTime.lock > 2 then
                        print("🔒 EXECUTING LOCK COMMAND")
                        lockCommand()
                        lastTime.lock = now
                    elseif cmd_id == "4" and now - lastTime.unlock > 2 then
                        print("🔓 EXECUTING UNLOCK COMMAND")
                        unlockCommand()
                        lastTime.unlock = now
                    elseif cmd_id == "2" and now - lastTime.mirror > 2 then
                        print("🪞 EXECUTING MIRROR COMMAND")
                        -- Mirror command placeholder
                        lastTime.mirror = now
                    else
                        print("⏰ Command ignored (too frequent or unknown): ID=" .. cmd_id)
                    end
                else
                    print("⚠️ Data too short (need >8 bytes, got " .. #data .. ")")
                end
            else
                print("❌ Failed to read UART data or empty data")
            end
            print("=== UART EVENT PROCESSED ===")
        end)

        print("✅ UART event handler registered successfully")
        print("=== UART INITIALIZATION COMPLETE ===")
        print("Monitoring Pin 17 (GPIO18/MAIN_RXD) for incoming data...")

        -- Keep the task alive for periodic status reporting
        local status_counter = 0
        while true do
            sys.wait(5000)  -- Wait 5 seconds between status checks
            status_counter = status_counter + 1

            if status_counter % 6 == 0 then  -- Every 30 seconds
                local rx_size = uart.rxSize and uart.rxSize(uart_id) or "unknown"
                print("UART Status: ID=" .. uart_id ..
                      ", RX buffer=" .. tostring(rx_size) .. " bytes" ..
                      ", Pin 17 (GPIO18/MAIN_RXD) monitoring active")
            end
        end
    else
        print("❌ Failed to set up UART event handler")
        print("=== UART INITIALIZATION FAILED ===")
    end
end

-- Simplified monitoring
local function monitorVoltage()
    local new_voltage = readVoltage() or 0
    if new_voltage > 0 and last_voltage > 0 then
        local diff = math.abs(new_voltage - last_voltage)
        if diff >= (vars.voltage_threshold or 0.5) and vars.voltage_notify_flag then
            if mqtt_module.is_ready() then
                publishSensorData()
            end
        end
    end
    last_voltage = new_voltage
end

-- Simplified OTA functions
local function update_cb(result)
    ota_in_progress = false
    print("OTA update " .. (result and "success" or "failed"))
    if result and rtos and rtos.reboot then
        sys.taskInit(function() sys.wait(2000); rtos.reboot() end)
    end
end

local function startOTAUpdate()
    if ota_in_progress or not update_available then return false end
    print("Starting OTA update...")
    ota_in_progress = true
    sys.taskInit(function()
        sys.waitUntil("IP_READY", 30000)
        update.request(update_cb)
    end)
    return true
end

-- Simplified MQTT message handler
local function handleMQTTMessage(topic, payload)
    local success, data = pcall(json.decode, payload)
    if success and data and data.command then
        print("Command:", data.command)
        playBeep("check")

        if data.command == "check" then
            publishSensorData()
        elseif data.command == "lock" then
            lockCommand(); publishSensorData()
        elseif data.command == "unlock" then
            unlockCommand(); publishSensorData()
        elseif data.command == "as" then
            if vars.isLicensed then asCommand() end
            publishSensorData()
        elseif data.command == "untar" then
            untarCommand(); publishSensorData()
        elseif data.command == "update" then
            startOTAUpdate()
        elseif data.command == "restart" then
            print("Restart command received via MQTT")
            sys.taskInit(function()
                sys.wait(2000)
                if rtos and rtos.reboot then
                    rtos.reboot()
                end
            end)
        elseif data.command == "ota_debug" then
            -- Debug OTA configuration
            print("=== OTA Debug Information ===")
            print("PROJECT:", PROJECT)
            print("VERSION:", VERSION)
            print("PRODUCT_KEY:", PRODUCT_KEY)
            if mobile and mobile.imei then
                print("IMEI:", mobile.imei())
            end
            print("Available modules:")
            print("- rtos:", rtos and "available" or "not available")
            print("- mobile:", mobile and "available" or "not available")
            print("- libfota:", libfota and "available" or "not available")
            if update and update.test then
                print("Running OTA module test...")
                update.test()
            end
            print("=== End OTA Debug ===")
        elseif data.command == "uart_test" then
            -- UART testing command via MQTT (receive-only mode)
            print("=== MQTT UART TEST COMMAND (LuatOS API) ===")
            print("Note: UART is configured for receive-only on Pin 17 (GPIO18/MAIN_RXD)")

            if uart then
                -- Check UART status using official API
                local uart_id = 1
                if uart.exist and not uart.exist(1) then
                    uart_id = 0
                end

                print("Using UART" .. uart_id .. " for testing")

                if uart.rxSize then
                    local rx_size = uart.rxSize(uart_id)
                    print("Current UART RX buffer: " .. tostring(rx_size) .. " bytes")

                    if rx_size and rx_size > 0 and uart.read then
                        local buffered_data = uart.read(uart_id, rx_size)
                        if buffered_data then
                            print("Buffered data: [" .. buffered_data .. "]")
                            print("Data length: " .. #buffered_data .. " bytes")
                            print("Data (hex): " .. string.gsub(buffered_data, ".", function(c)
                                return string.format("%02X ", string.byte(c))
                            end))
                        end
                    else
                        print("No data in RX buffer")
                    end
                else
                    print("❌ uart.rxSize function not available")
                end

                -- Test sending data (for loopback testing)
                local test_message = data.message or "Test from MQTT"
                if uart.write then
                    print("Sending test message: [" .. test_message .. "]")
                    local sent = uart.write(uart_id, test_message)
                    print("Bytes sent: " .. tostring(sent))
                else
                    print("❌ uart.write function not available")
                end
            else
                print("❌ UART module not available")
            end
        elseif data.command == "uart_status" then
            -- UART status command via MQTT using LuatOS API
            print("=== UART STATUS REQUEST (LuatOS API) ===")
            print("UART module available: " .. tostring(uart ~= nil))

            if uart then
                print("UART functions available:")
                print("  uart.setup: " .. tostring(uart.setup ~= nil))
                print("  uart.read: " .. tostring(uart.read ~= nil))
                print("  uart.write: " .. tostring(uart.write ~= nil))
                print("  uart.rxSize: " .. tostring(uart.rxSize ~= nil))
                print("  uart.on: " .. tostring(uart.on ~= nil))
                print("  uart.exist: " .. tostring(uart.exist ~= nil))

                -- Check which UART ports exist
                if uart.exist then
                    for i = 0, 2 do
                        local exists = uart.exist(i)
                        print("UART" .. i .. " exists: " .. tostring(exists))

                        if exists and uart.rxSize then
                            local rx_size = uart.rxSize(i)
                            print("UART" .. i .. " RX buffer: " .. tostring(rx_size) .. " bytes")
                        end
                    end
                else
                    print("uart.exist function not available")
                end
            end

            -- Check GPIO status (Pin 17 = GPIO18 = MAIN_RXD)
            if gpio then
                print("GPIO module available: true")
                print("Pin 17 (GPIO18/MAIN_RXD) state: " .. tostring(gpio.get(18)))
            else
                print("GPIO module not available")
            end
        elseif data.command == "uart_pin_test" then
            -- Test UART pin configuration (receive-only)
            print("=== UART PIN TEST (Pin 17 / GPIO18 / MAIN_RXD) ===")

            if gpio then
                -- Test GPIO18 (Pin 17 - MAIN_RXD)
                print("Testing Pin 17 (GPIO18/MAIN_RXD)...")

                -- Read current pin state
                local gpio18_state = gpio.get(18)
                print("Pin 17 (GPIO18/MAIN_RXD) current state: " .. tostring(gpio18_state))

                -- Test pin stability over time
                print("Testing Pin 17 (GPIO18) stability...")
                for i = 1, 5 do
                    local state = gpio.get(18)
                    print("Pin 17 (GPIO18) reading " .. i .. ": " .. tostring(state))
                    sys.wait(100)
                end

                -- Reconfigure GPIO18 (Pin 17) for UART RX
                gpio.setup(18, nil, gpio.PULLUP)
                print("Pin 17 (GPIO18/MAIN_RXD) reconfigured with pullup")
                print("Note: Pin 17 has hardware pull-up to VDD_EXT and wake-up capability")
            else
                print("GPIO module not available for pin testing")
            end
        end
    end
end

-- MQTT initial data publishing handler
local function handleInitialDataPublish()
    print("Publishing initial sensor data to MQTT")
    -- Call publishSensorData which will start a task
    if publishSensorData() then
        print("Started task to publish initial sensor data")
    else
        print("Failed to start task for publishing initial sensor data")
    end
end

-- Simplified SMS command processing
local function processSmsCommand(command, callback_number)
    if not vars.isLicensed and (command == "lock" or command == "unlock" or command == "as") then
        sendSms(callback_number, "License expired")
        return
    end

    if command == "check" then
        local temp_hum = readSHTC3()
        local voltage = readVoltage()
        local msg = string.format("Batt: %.2fV | Temp: %.1fC", voltage, temp_hum.temperature)
        sendSms(callback_number, msg)
    elseif command == "lock" then
        lockCommand(); sendSms(callback_number, "locked")
    elseif command == "unlock" then
        unlockCommand(); sendSms(callback_number, "unlocked")
    elseif command == "as" then
        asCommand(); sendSms(callback_number, "started")
    elseif command == "untar" then
        untarCommand(); sendSms(callback_number, "stopped")
    elseif command == "version" then
        sendSms(callback_number, VERSION)
    end
end

-- Simplified initialization
local function initTask()
    print("Air780EG Enhanced v" .. VERSION)

    -- Initialize core modules
    PinModule.setupPins()
    initSHTC3()
    initADC()
    initGPS()
    initSMS()
    my_utils.loadConfiguration()
    mqtt_module.init()

    -- Basic subscriptions
    sys.subscribe("MQTT_MESSAGE_RECEIVED", handleMQTTMessage)
    sys.subscribe("MQTT_PUBLISH_INITIAL_DATA", function() publishSensorData() end)
    sys.subscribe("MQTT_CONNECTED", function()
        PinModule.setCloudLED(1)
        if not mqtt_connection_beep_played then
            mqtt_connection_beep_played = true
            playBeep("check")
        end
    end)
    sys.subscribe("MQTT_DISCONNECTED", function()
        PinModule.setCloudLED(0)
        mqtt_connection_beep_played = false
    end)

    -- Start essential tasks
    sys.taskInit(sensorTask)
    sys.taskInit(uartTask)
    sys.timerLoopStart(monitorVoltage, 10000)

    -- SMS processing task
    sys.taskInit(function()
        while true do
            if vars.sms_data then
                processSmsCommand(vars.sms_data, vars.callback_number)
                vars.sms_data = nil
                vars.callback_number = nil
            end
            sys.wait(1000)
        end
    end)



    print("Air780EG Enhanced v" .. VERSION .. " - Ready!")
    print("MQTT commands: check, lock, unlock, as, untar, update, restart, ota_debug, uart_test, uart_status, uart_pin_test")
    print("SMS commands: check, lock, unlock, as, untar, version, restart")
    print("UART: Monitoring Pin 17 (GPIO18/MAIN_RXD) for incoming commands")
end

-- Main application entry point
local function main()
    -- Start the initialization task in a coroutine
    sys.taskInit(initTask)

    -- Keep the system running
    sys.run()
end

-- Start the application
main()
