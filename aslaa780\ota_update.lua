-- ota_update.lua - Simple OTA update module for Air780EG
-- This module provides OTA functionality using libfota API

local ota_update = {}

-- Load required modules
local sys = require("sys")

-- OTA state variables
local ota_in_progress = false
local ota_callback = nil
local download_progress_callback = nil

-- Try to load libfota module
local libfota_available = false
local libfota = nil

-- Check for libfota module
if pcall(require, "libfota") then
    libfota = require("libfota")
    libfota_available = true
    print("libfota module loaded successfully")
else
    print("libfota module not available")
end

-- Function to get device IMEI
local function getIMEI()
    if mobile and mobile.imei then
        return mobile.imei()
    end
    return "unknown"
end

-- Function to get current firmware version info
local function getFirmwareInfo()
    local coreVer = "unknown_V0"

    -- Try different methods to get version info
    if rtos and rtos.get_version then
        coreVer = rtos.get_version()
    elseif _G.VERSION then
        coreVer = "LuatOS_V" .. _G.VERSION
    else
        coreVer = "LuatOS_V1"
    end

    local coreName1, coreName2 = coreVer:match("(.-)_V%d+(_.+)")
    local coreVersion = tonumber(coreVer:match(".-_V(%d+)"))

    local info = {
        core_name = (coreName1 or "LuatOS") .. (coreName2 or ""),
        core_version = coreVersion or 1,
        project = _G.PROJECT or "unknown",
        version = _G.VERSION or "1.0.0",
        product_key = _G.PRODUCT_KEY or ""
    }

    print("Firmware info:")
    print("- Core version:", coreVer)
    print("- Core name:", info.core_name)
    print("- Core version number:", info.core_version)
    print("- Project:", info.project)
    print("- Version:", info.version)
    print("- Product key:", info.product_key)

    return info
end



-- Progress callback wrapper for libfota
local function progress_wrapper(progress)
    if download_progress_callback then
        download_progress_callback(progress)
    end
    print("OTA download progress:", progress .. "%")
end

-- FOTA callback function for libfota
local function libfota_callback(result)
    print("libfota callback result:", result)

    -- Provide more detailed error information
    local error_messages = {
        [0] = "Success",
        [1] = "Download failed",
        [2] = "Verification failed",
        [3] = "Write failed",
        [4] = "Server response error (check server message)",
        [5] = "Network error",
        [6] = "Server error",
        [7] = "Timeout",
        [8] = "Memory error",
        [9] = "Invalid parameter"
    }

    local error_msg = error_messages[result] or ("Unknown error code: " .. tostring(result))
    print("OTA result details:", error_msg)

    ota_in_progress = false

    if ota_callback then
        if result == 0 then
            ota_callback(true)  -- Success
        else
            ota_callback(false) -- Failed
        end
    else
        -- No callback provided, auto-restart on success
        if result == 0 then
            print("OTA update successful, restarting...")
            if sys and sys.restart then
                sys.restart("OTA_UPDATE_SUCCESS")
            elseif rtos and rtos.reboot then
                rtos.reboot()
            else
                print("No restart function available")
            end
        else
            print("OTA update failed:", error_msg)
        end
    end
end

-- Start OTA update process
function ota_update.request(callback)
    print("OTA update request started")

    -- Print firmware info for debugging
    local firmware_info = getFirmwareInfo()
    print("Current firmware configuration:")
    print("- PROJECT:", _G.PROJECT)
    print("- VERSION:", _G.VERSION)
    print("- PRODUCT_KEY:", _G.PRODUCT_KEY)
    print("- IMEI:", getIMEI())

    -- Check what version libfota will actually send
    print("Version info that will be sent to server:")
    print("- Application version (VERSION):", _G.VERSION)
    print("- Core version info:", firmware_info.core_name, firmware_info.core_version)

    -- The server might be expecting the application version, not core version
    print("Expected URL pattern:")
    local expected_url = string.format(
        "http://iot.openluat.com/api/site/firmware_upgrade?project_key=%s&firmware_name=%s&version=%s&imei=%s",
        _G.PRODUCT_KEY or "unknown",
        (_G.PROJECT or "unknown") .. "_LuatOS-SoC_EC618",
        _G.VERSION or "unknown",
        getIMEI()
    )
    print("Expected:", expected_url)

    print("Debug info:")
    print("- ota_in_progress:", ota_in_progress)
    print("- libfota available:", libfota_available and "yes" or "no")
    print("- libfota.request available:", libfota and libfota.request and "yes" or "no")
    print("- mobile module available:", mobile and "yes" or "no")

    if ota_in_progress then
        print("OTA update already in progress")
        if callback then callback(false) end
        return false
    end

    -- Check if libfota is available
    if not libfota_available or not libfota or not libfota.request then
        print("libfota not available - cannot perform OTA update")
        print("Available modules:")
        if libfota then
            for k, v in pairs(libfota) do
                if type(v) == "function" then
                    print("- libfota." .. k)
                end
            end
        end
        if callback then callback(false) end
        return false
    end

    ota_in_progress = true
    ota_callback = callback

    print("Starting libfota.request...")

    -- Start the libfota request
    if sys and sys.taskInit then
        sys.taskInit(function()
            -- Wait for network to be ready
            if sys.waitUntil then
                sys.waitUntil("IP_READY", 30000)
            else
                -- Fallback: wait a bit for network
                if sys.wait then
                    sys.wait(5000)
                end
            end

            -- Try to set version before making request
            if libfota.setVersion then
                print("Setting application version for OTA:", _G.VERSION)
                libfota.setVersion(_G.VERSION)
            end

            -- Use libfota.request for Air780E
            local success, result = pcall(libfota.request, libfota_callback)

            if not success then
                print("Error calling libfota.request:", result)
                ota_in_progress = false
                if ota_callback then ota_callback(false) end
            elseif not result then
                print("Failed to start libfota.request")
                ota_in_progress = false
                if ota_callback then ota_callback(false) end
            else
                print("libfota.request started successfully")
            end
        end)
    else
        -- Fallback: call libfota.request directly
        print("sys.taskInit not available, calling libfota.request directly")

        -- Try to set version before making request
        if libfota.setVersion then
            print("Setting application version for OTA:", _G.VERSION)
            libfota.setVersion(_G.VERSION)
        end

        local success, result = pcall(libfota.request, libfota_callback)

        if not success then
            print("Error calling libfota.request:", result)
            ota_in_progress = false
            if ota_callback then ota_callback(false) end
        elseif not result then
            print("Failed to start libfota.request")
            ota_in_progress = false
            if ota_callback then ota_callback(false) end
        else
            print("libfota.request started successfully")
        end
    end

    return true
end

-- Set download progress callback
function ota_update.setDownloadProcessCbFnc(callback)
    download_progress_callback = callback

    -- Set the progress callback for libfota if available
    if libfota and libfota.setProgressCb then
        libfota.setProgressCb(progress_wrapper)
    end
end

-- Get update message (placeholder for compatibility)
function ota_update.getUpdateMsg()
    return "OTA update using custom implementation"
end

-- Check if OTA is in progress
function ota_update.isInProgress()
    return ota_in_progress
end

-- Test function to verify module functionality
function ota_update.test()
    print("=== OTA Module Test ===")
    print("Module loaded successfully")

    local firmware_info = getFirmwareInfo()

    print("Required functions check:")
    print("- libfota module:", libfota_available and "✓" or "✗")
    print("- libfota.request:", libfota and libfota.request and "✓" or "✗")
    print("- mobile.imei:", mobile and mobile.imei and "✓" or "✗")

    if libfota then
        print("Available libfota functions:")
        for k, v in pairs(libfota) do
            if type(v) == "function" then
                print("- libfota." .. k)
            end
        end

        -- Test version setting capability
        if libfota.setVersion then
            print("✓ libfota.setVersion is available - can override version")
        else
            print("✗ libfota.setVersion not available - using core version")
        end
    end

    print("=== Test Complete ===")
    return libfota_available and libfota and libfota.request
end

return ota_update
